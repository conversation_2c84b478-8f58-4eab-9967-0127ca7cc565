package adhoc.data_persistence

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

import java.util.concurrent.TimeUnit

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class DataPersistenceITSpec extends BaseAdhocITSpec {

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    @Autowired
    BcmonitoringConfigurationProperties properties

    @Autowired
    AbiParser abiParser

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to S3
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "AccessCtrl",
                "Token",
                "Account",
                "Provider"
        ])
    }

    def cleanup() {
        cleanupCommon()
    }

    /**
     * Should events successfully stores parsed events to DynamoDB Events table
     * Verifies service correctly stores parsed events to DynamoDB Events table
     * Expected: Events are correctly stored in DynamoDB Events table and logs "Success to register event"
     */
    def "Should events successfully stores parsed events to DynamoDB Events table"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"
        // Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
        def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
        setUpEventStream(mockNotifications)
        // Setup mock Web3j to return blocks with transactions and events
        def eventLogConfigs = [
                [logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
                [logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
                [logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
        ]
        setupMockWeb3jWithEvents(eventLogConfigs)

        // Setup mock pending events
        def mockPendingEventLogs = createMockPendingEventLogs([
                "addProviderRole",
                "addTokenByProvider"
        ], 200L, "0xabc")
        setUpPendingEvent(mockPendingEventLogs)

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()


    }
}
