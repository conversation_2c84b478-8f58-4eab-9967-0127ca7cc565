package adhoc.data_persistence

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class DataPersistenceITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@Autowired
	AbiParser abiParser

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should events successfully stores parsed events to DynamoDB Events table
	 * Verifies service correctly stores parsed events to DynamoDB Events table
	 * Expected: Events are correctly stored in DynamoDB Events table and logs "Success to register event"
	 */
	def "Should events successfully stores parsed events to DynamoDB Events table"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"roleGranted",
			"roleRevoked"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }

		and: "Events are correctly stored in DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5
		def roleAdminChangedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleAdminChanged"
		}
		def roleGrantedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleGranted"
		}
		def roleRevokedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleRevoked"
		}
		def addProviderRoleEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddProviderRole"
		}
		def addTokenByProviderEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddTokenByProvider"
		}
		assert roleAdminChangedEvent != null && roleGrantedEvent != null && roleRevokedEvent != null && addProviderRoleEvent != null && addTokenByProviderEvent != null
	}
}
